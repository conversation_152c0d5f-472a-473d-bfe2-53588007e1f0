import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Select,
  Space,
  Pagination,
  Spin,
  Empty,
  Breadcrumb,
  Tag,
  Slider,
  Checkbox,
  Collapse,
  Typography,
  Input,
  message
} from 'antd';
import {
  FilterOutlined,
  AppstoreOutlined,
  BarsOutlined,
  SearchOutlined,
  ClearOutlined,
  HomeOutlined,
  BookOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useNavigate, useSearchParams } from 'react-router-dom';
import EnhancedBookCard from '../../components/business/EnhancedBookCard';
import AdvancedSearch from '../../components/business/AdvancedSearch';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import Loading from '../../components/ui/Loading';
import { theme } from '../../styles/theme';
import { booksService } from '../../services/books';
import { categoriesService } from '../../services/categories';

const { Title, Text } = Typography;
const { Option } = Select;
const { Panel } = Collapse;

const BooksListContainer = styled.div`
  min-height: 100vh;
  background: ${theme.colors.gradients.cool};

  .page-header {
    background: white;
    padding: ${theme.spacing[6]} 0;
    border-bottom: 1px solid ${theme.colors.gray[200]};
    box-shadow: ${theme.boxShadow.sm};
    
    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 ${theme.spacing[6]};

      .breadcrumb {
        margin-bottom: ${theme.spacing[4]};

        .ant-breadcrumb-link {
          color: ${theme.colors.gray[600]};
          transition: color ${theme.animation.duration.fast};

          &:hover {
            color: ${theme.colors.primary[500]};
          }
        }
      }

      .header-title {
        display: flex;
        align-items: center;
        justify-content: space-between;

        @media (max-width: 768px) {
          flex-direction: column;
          gap: ${theme.spacing[4]};
          align-items: flex-start;
        }

        .title-left {
          display: flex;
          align-items: center;
          gap: ${theme.spacing[3]};

          .page-title {
            margin: 0;
            font-size: ${theme.typography.fontSize['3xl']};
            font-weight: ${theme.typography.fontWeight.black};
            background: ${theme.colors.gradients.primary};
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }

          .result-count {
            color: ${theme.colors.gray[500]};
            font-size: ${theme.typography.fontSize.sm};
            font-weight: ${theme.typography.fontWeight.medium};
            background: ${theme.colors.gray[100]};
            padding: ${theme.spacing[1]} ${theme.spacing[3]};
            border-radius: ${theme.borderRadius.full};
          }
        }
        
        .title-right {
          display: flex;
          align-items: center;
          gap: ${theme.spacing[3]};

          .view-toggle {
            display: flex;
            border: 1px solid ${theme.colors.gray[300]};
            border-radius: ${theme.borderRadius.lg};
            overflow: hidden;
            background: white;
            box-shadow: ${theme.boxShadow.sm};

            .toggle-btn {
              padding: ${theme.spacing[2]} ${theme.spacing[3]};
              border: none;
              background: transparent;
              cursor: pointer;
              transition: all ${theme.animation.duration.base};
              color: ${theme.colors.gray[600]};
              font-size: ${theme.typography.fontSize.base};

              &.active {
                background: ${theme.colors.gradients.primary};
                color: white;
                box-shadow: ${theme.boxShadow.sm};
              }

              &:hover:not(.active) {
                background: ${theme.colors.gray[50]};
                color: ${theme.colors.primary[500]};
              }
            }
          }
        }
      }
    }
  }
  
  .page-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    display: flex;
    gap: 24px;
    
    @media (max-width: 768px) {
      flex-direction: column;
      padding: 16px;
    }
    
    .filters-sidebar {
      width: 300px;
      flex-shrink: 0;

      @media (max-width: 768px) {
        width: 100%;
      }

      .filters-card {
        background: white;
        border-radius: ${theme.borderRadius['2xl']};
        box-shadow: ${theme.boxShadow.lg};
        border: 1px solid ${theme.colors.gray[200]};
        overflow: hidden;

        .ant-card-body {
          padding: ${theme.spacing[6]};
        }

        .filters-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: ${theme.spacing[6]};
          padding-bottom: ${theme.spacing[4]};
          border-bottom: 1px solid ${theme.colors.gray[200]};

          .filters-title {
            font-size: ${theme.typography.fontSize.xl};
            font-weight: ${theme.typography.fontWeight.bold};
            color: ${theme.colors.gray[800]};
            display: flex;
            align-items: center;
            gap: ${theme.spacing[2]};
          }

          .clear-filters {
            color: ${theme.colors.gray[500]};
            font-size: ${theme.typography.fontSize.sm};
            cursor: pointer;
            transition: color ${theme.animation.duration.fast};
            display: flex;
            align-items: center;
            gap: ${theme.spacing[1]};

            &:hover {
              color: ${theme.colors.primary[500]};
            }
          }
        }
        
        .filter-section {
          margin-bottom: ${theme.spacing[8]};

          .filter-title {
            font-size: ${theme.typography.fontSize.base};
            font-weight: ${theme.typography.fontWeight.semibold};
            margin-bottom: ${theme.spacing[4]};
            color: ${theme.colors.gray[700]};
            position: relative;

            &::after {
              content: '';
              position: absolute;
              bottom: -8px;
              left: 0;
              width: 30px;
              height: 2px;
              background: ${theme.colors.gradients.primary};
              border-radius: ${theme.borderRadius.full};
            }
          }

          .price-range {
            .ant-slider {
              margin: ${theme.spacing[4]} 0;

              .ant-slider-rail {
                background: ${theme.colors.gray[200]};
              }

              .ant-slider-track {
                background: ${theme.colors.gradients.primary};
              }

              .ant-slider-handle {
                border-color: ${theme.colors.primary[500]};

                &:hover {
                  border-color: ${theme.colors.primary[600]};
                }
              }
            }

            .price-inputs {
              display: flex;
              gap: ${theme.spacing[2]};
              align-items: center;
              margin-top: ${theme.spacing[3]};

              .price-input {
                flex: 1;
                border-radius: ${theme.borderRadius.md};

                &:focus {
                  border-color: ${theme.colors.primary[500]};
                  box-shadow: 0 0 0 2px ${theme.colors.primary[100]};
                }
              }
            }
          }
          
          .condition-options {
            .ant-checkbox-group {
              display: flex;
              flex-direction: column;
              gap: 8px;
            }
          }
          
          .category-list {
            max-height: 240px;
            overflow-y: auto;

            /* 自定义滚动条 */
            &::-webkit-scrollbar {
              width: 4px;
            }

            &::-webkit-scrollbar-track {
              background: ${theme.colors.gray[100]};
              border-radius: ${theme.borderRadius.full};
            }

            &::-webkit-scrollbar-thumb {
              background: ${theme.colors.gray[300]};
              border-radius: ${theme.borderRadius.full};

              &:hover {
                background: ${theme.colors.gray[400]};
              }
            }

            .category-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              cursor: pointer;
              border-radius: ${theme.borderRadius.md};
              padding: ${theme.spacing[3]} ${theme.spacing[4]};
              margin-bottom: ${theme.spacing[1]};
              transition: all ${theme.animation.duration.base};
              position: relative;

              &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                width: 3px;
                background: ${theme.colors.primary[500]};
                border-radius: 0 ${theme.borderRadius.sm} ${theme.borderRadius.sm} 0;
                opacity: 0;
                transition: opacity ${theme.animation.duration.base};
              }

              &:hover {
                background: ${theme.colors.gray[50]};
                transform: translateX(4px);
              }

              &.active {
                background: ${theme.colors.primary[50]};
                color: ${theme.colors.primary[600]};

                &::before {
                  opacity: 1;
                }
              }

              .category-name {
                flex: 1;
                font-weight: ${theme.typography.fontWeight.medium};
              }

              .category-count {
                font-size: ${theme.typography.fontSize.xs};
                color: ${theme.colors.gray[500]};
                background: ${theme.colors.gray[100]};
                padding: ${theme.spacing[1]} ${theme.spacing[2]};
                border-radius: ${theme.borderRadius.full};
                font-weight: ${theme.typography.fontWeight.medium};
              }
            }
          }
        }
      }
    }
    
    .books-content {
      flex: 1;

      .content-header {
        background: white;
        padding: ${theme.spacing[5]} ${theme.spacing[6]};
        border-radius: ${theme.borderRadius['2xl']};
        margin-bottom: ${theme.spacing[4]};
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: ${theme.boxShadow.md};
        border: 1px solid ${theme.colors.gray[200]};

        @media (max-width: 768px) {
          flex-direction: column;
          gap: ${theme.spacing[4]};
          align-items: stretch;
        }

        .search-section {
          flex: 1;
          max-width: 400px;

          @media (max-width: 768px) {
            max-width: none;
          }

          .ant-input-search {
            .ant-input {
              border-radius: ${theme.borderRadius.lg};
              border-color: ${theme.colors.gray[300]};

              &:focus {
                border-color: ${theme.colors.primary[500]};
                box-shadow: 0 0 0 2px ${theme.colors.primary[100]};
              }
            }

            .ant-btn {
              border-radius: 0 ${theme.borderRadius.lg} ${theme.borderRadius.lg} 0;
              background: ${theme.colors.gradients.primary};
              border-color: ${theme.colors.primary[500]};

              &:hover {
                background: ${theme.colors.primary[600]};
                border-color: ${theme.colors.primary[600]};
              }
            }
          }
        }

        .sort-section {
          display: flex;
          align-items: center;
          gap: ${theme.spacing[3]};

          .sort-label {
            color: ${theme.colors.gray[600]};
            font-size: ${theme.typography.fontSize.sm};
            font-weight: ${theme.typography.fontWeight.medium};
          }

          .sort-select {
            min-width: 160px;

            .ant-select-selector {
              border-radius: ${theme.borderRadius.lg};
              border-color: ${theme.colors.gray[300]};

              &:hover {
                border-color: ${theme.colors.primary[400]};
              }
            }

            &.ant-select-focused .ant-select-selector {
              border-color: ${theme.colors.primary[500]};
              box-shadow: 0 0 0 2px ${theme.colors.primary[100]};
            }
          }
        }
      }
      
      .books-grid {
        display: grid;
        gap: ${theme.spacing[6]};

        &.grid-view {
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));

          @media (max-width: 768px) {
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
          }

          @media (max-width: 480px) {
            grid-template-columns: 1fr;
          }
        }

        &.list-view {
          grid-template-columns: 1fr;
          gap: ${theme.spacing[4]};

          .book-card {
            .ant-card-body {
              padding: ${theme.spacing[4]};
            }
          }
        }
      }

      .pagination-container {
        margin-top: ${theme.spacing[10]};
        text-align: center;
        background: white;
        padding: ${theme.spacing[6]};
        border-radius: ${theme.borderRadius['2xl']};
        box-shadow: ${theme.boxShadow.md};
        border: 1px solid ${theme.colors.gray[200]};

        .pagination-info {
          margin-bottom: ${theme.spacing[4]};
          color: ${theme.colors.gray[600]};
          font-size: ${theme.typography.fontSize.sm};
          font-weight: ${theme.typography.fontWeight.medium};
        }

        .ant-pagination {
          .ant-pagination-item {
            border-radius: ${theme.borderRadius.md};
            border-color: ${theme.colors.gray[300]};

            &:hover {
              border-color: ${theme.colors.primary[400]};
            }

            &.ant-pagination-item-active {
              background: ${theme.colors.gradients.primary};
              border-color: ${theme.colors.primary[500]};
            }
          }

          .ant-pagination-prev,
          .ant-pagination-next {
            border-radius: ${theme.borderRadius.md};
            border-color: ${theme.colors.gray[300]};

            &:hover {
              border-color: ${theme.colors.primary[400]};
            }
          }
        }
      }

      .empty-state {
        text-align: center;
        padding: ${theme.spacing[20]} ${theme.spacing[6]};
        background: white;
        border-radius: ${theme.borderRadius['2xl']};
        box-shadow: ${theme.boxShadow.lg};
        border: 1px solid ${theme.colors.gray[200]};

        .empty-icon {
          font-size: ${theme.typography.fontSize['6xl']};
          color: ${theme.colors.gray[300]};
          margin-bottom: ${theme.spacing[6]};
          filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
        }

        .empty-title {
          font-size: ${theme.typography.fontSize['2xl']};
          color: ${theme.colors.gray[700]};
          margin-bottom: ${theme.spacing[2]};
          font-weight: ${theme.typography.fontWeight.bold};
        }

        .empty-desc {
          color: ${theme.colors.gray[500]};
          margin-bottom: ${theme.spacing[8]};
          font-size: ${theme.typography.fontSize.base};
          line-height: ${theme.typography.lineHeight.relaxed};
        }
      }
    }
  }
`;

interface BooksListPageProps {}

const BooksListPage: React.FC<BooksListPageProps> = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  
  const [loading, setLoading] = useState(false);
  const [books, setBooks] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  
  const [filters, setFilters] = useState({
    keyword: searchParams.get('keyword') || '',
    category: searchParams.get('category') || '',
    minPrice: 0,
    maxPrice: 1000,
    condition: [] as string[],
    sort: searchParams.get('sort') || 'created_at_DESC'
  });
  
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  useEffect(() => {
    loadCategories();
  }, []);

  useEffect(() => {
    loadBooks();
  }, [filters, pagination.current]);

  const loadCategories = async () => {
    try {
      const response = await categoriesService.getCategories();
      if (response.success && response.data) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error('加载分类失败:', error);
    }
  };

  const loadBooks = async () => {
    try {
      setLoading(true);
      
      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        keyword: filters.keyword || undefined,
        category_id: filters.category || undefined,
        min_price: filters.minPrice > 0 ? filters.minPrice : undefined,
        max_price: filters.maxPrice < 1000 ? filters.maxPrice : undefined,
        condition: filters.condition.length > 0 ? filters.condition[0] : undefined,
        sort: filters.sort
      };

      const response = await booksService.getBooks(params);
      
      if (response.success) {
        setBooks(response.data.books);
        setPagination(prev => ({
          ...prev,
          total: response.data.pagination.total_items
        }));
      }
    } catch (error) {
      message.error('加载图书列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (searchFilters: any) => {
    setFilters(prev => ({
      ...prev,
      ...searchFilters
    }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleClearFilters = () => {
    setFilters({
      keyword: '',
      category: '',
      minPrice: 0,
      maxPrice: 1000,
      condition: [],
      sort: 'created_at_DESC'
    });
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize || prev.pageSize
    }));
  };

  const sortOptions = [
    { label: '最新发布', value: 'created_at_DESC' },
    { label: '价格从低到高', value: 'price_ASC' },
    { label: '价格从高到低', value: 'price_DESC' },
    { label: '销量从高到低', value: 'sales_count_DESC' },
    { label: '评分从高到低', value: 'rating_DESC' }
  ];

  const conditionOptions = [
    { label: '全新', value: '全新' },
    { label: '九成新', value: '九成新' },
    { label: '八成新', value: '八成新' },
    { label: '七成新', value: '七成新' },
    { label: '六成新', value: '六成新' }
  ];

  return (
    <BooksListContainer>
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <Breadcrumb className="breadcrumb">
            <Breadcrumb.Item>
              <HomeOutlined />
              <span onClick={() => navigate('/')} style={{ cursor: 'pointer', marginLeft: 8 }}>
                首页
              </span>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <BookOutlined />
              图书列表
            </Breadcrumb.Item>
            {filters.category && (
              <Breadcrumb.Item>
                {categories.find(c => c.id === filters.category)?.name}
              </Breadcrumb.Item>
            )}
          </Breadcrumb>
          
          <div className="header-title">
            <div className="title-left">
              <Title level={2} className="page-title">图书列表</Title>
              <span className="result-count">
                共找到 {pagination.total} 本图书
              </span>
            </div>
            <div className="title-right">
              <div className="view-toggle">
                <button
                  className={`toggle-btn ${viewMode === 'grid' ? 'active' : ''}`}
                  onClick={() => setViewMode('grid')}
                >
                  <AppstoreOutlined />
                </button>
                <button
                  className={`toggle-btn ${viewMode === 'list' ? 'active' : ''}`}
                  onClick={() => setViewMode('list')}
                >
                  <BarsOutlined />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 页面内容 */}
      <div className="page-content">
        {/* 筛选侧边栏 */}
        <div className="filters-sidebar">
          <Card variant="elevated" className="filters-card">
            <div className="filters-header">
              <span className="filters-title">
                <FilterOutlined style={{ marginRight: 8 }} />
                筛选条件
              </span>
              <span className="clear-filters" onClick={handleClearFilters}>
                <ClearOutlined style={{ marginRight: 4 }} />
                清空
              </span>
            </div>

            {/* 高级搜索 */}
            <div className="filter-section">
              <AdvancedSearch
                onSearch={handleSearch}
                initialFilters={filters}
              />
            </div>

            {/* 分类筛选 */}
            <div className="filter-section">
              <div className="filter-title">图书分类</div>
              <div className="category-list">
                <div
                  className={`category-item ${!filters.category ? 'active' : ''}`}
                  onClick={() => handleFilterChange('category', '')}
                >
                  <span className="category-name">全部分类</span>
                </div>
                {categories.map(category => (
                  <div
                    key={category.id}
                    className={`category-item ${filters.category === category.id ? 'active' : ''}`}
                    onClick={() => handleFilterChange('category', category.id)}
                  >
                    <span className="category-name">{category.name}</span>
                    <span className="category-count">({category.book_count || 0})</span>
                  </div>
                ))}
              </div>
            </div>

            {/* 价格范围 */}
            <div className="filter-section">
              <div className="filter-title">价格范围</div>
              <div className="price-range">
                <Slider
                  range
                  min={0}
                  max={1000}
                  value={[filters.minPrice, filters.maxPrice]}
                  onChange={([min, max]) => {
                    handleFilterChange('minPrice', min);
                    handleFilterChange('maxPrice', max);
                  }}
                  marks={{
                    0: '¥0',
                    200: '¥200',
                    500: '¥500',
                    1000: '¥1000+'
                  }}
                />
                <div className="price-inputs">
                  <Input
                    className="price-input"
                    prefix="¥"
                    value={filters.minPrice}
                    onChange={(e) => handleFilterChange('minPrice', Number(e.target.value) || 0)}
                  />
                  <Text>至</Text>
                  <Input
                    className="price-input"
                    prefix="¥"
                    value={filters.maxPrice}
                    onChange={(e) => handleFilterChange('maxPrice', Number(e.target.value) || 1000)}
                  />
                </div>
              </div>
            </div>

            {/* 图书状况 */}
            <div className="filter-section">
              <div className="filter-title">图书状况</div>
              <div className="condition-options">
                <Checkbox.Group
                  options={conditionOptions}
                  value={filters.condition}
                  onChange={(values) => handleFilterChange('condition', values)}
                />
              </div>
            </div>
          </Card>
        </div>

        {/* 图书内容区 */}
        <div className="books-content">
          {/* 内容头部 */}
          <div className="content-header">
            <div className="search-section">
              <Input.Search
                placeholder="搜索图书名称、作者、ISBN..."
                value={filters.keyword}
                onChange={(e) => handleFilterChange('keyword', e.target.value)}
                onSearch={() => loadBooks()}
                enterButton={<SearchOutlined />}
              />
            </div>
            
            <div className="sort-section">
              <span className="sort-label">排序:</span>
              <Select
                className="sort-select"
                value={filters.sort}
                onChange={(value) => handleFilterChange('sort', value)}
              >
                {sortOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </div>
          </div>

          {/* 图书网格 */}
          <div style={{ position: 'relative' }}>
            {loading && (
              <Loading
                variant="book"
                size="large"
                text="正在搜索图书..."
                overlay
              />
            )}
            {books.length > 0 ? (
              <div className={`books-grid ${viewMode}-view`}>
                {books.map(book => (
                  <EnhancedBookCard
                    key={book.id}
                    book={book}
                  />
                ))}
              </div>
            ) : (
              <div className="empty-state">
                <BookOutlined className="empty-icon" />
                <div className="empty-title">暂无图书</div>
                <div className="empty-desc">
                  {filters.keyword || filters.category ? 
                    '没有找到符合条件的图书，请尝试调整筛选条件' : 
                    '暂时没有图书，请稍后再来看看'
                  }
                </div>
                <Button
                  variant="gradient"
                  gradient="primary"
                  onClick={handleClearFilters}
                  rounded
                >
                  清空筛选条件
                </Button>
              </div>
            )}
          </div>

          {/* 分页 */}
          {books.length > 0 && (
            <div className="pagination-container">
              <div className="pagination-info">
                显示第 {(pagination.current - 1) * pagination.pageSize + 1} - {Math.min(pagination.current * pagination.pageSize, pagination.total)} 条，共 {pagination.total} 条
              </div>
              <Pagination
                current={pagination.current}
                pageSize={pagination.pageSize}
                total={pagination.total}
                showSizeChanger
                showQuickJumper
                pageSizeOptions={['10', '20', '50', '100']}
                onChange={handlePageChange}
                onShowSizeChange={handlePageChange}
              />
            </div>
          )}
        </div>
      </div>
    </BooksListContainer>
  );
};

export default BooksListPage;
